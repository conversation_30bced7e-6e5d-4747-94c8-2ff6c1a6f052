<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业邮箱配置指南 - 网红营销智能体</title>
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --dark-bg: #0f0f1e;
            --card-bg: rgba(26, 27, 46, 0.8);
            --accent-purple: #7c3aed;
            --accent-blue: #4f46e5;
            --text-primary: #ffffff;
            --text-secondary: #a5a6b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-primary);
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* 动态背景效果 */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(80px);
            opacity: 0.3;
            animation: float 20s infinite ease-in-out;
        }

        .orb-1 {
            width: 600px;
            height: 600px;
            background: var(--accent-purple);
            top: -300px;
            right: -200px;
        }

        .orb-2 {
            width: 400px;
            height: 400px;
            background: var(--accent-blue);
            bottom: -200px;
            left: -100px;
            animation-delay: -5s;
        }

        .orb-3 {
            width: 500px;
            height: 500px;
            background: #ec4899;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: -10s;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(30px, -30px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
        }

        /* 导航栏 */
        .navbar {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(15, 15, 30, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            padding: 1rem 0;
        }

        /* 主要内容区域 */
        .main-content {
            position: relative;
            z-index: 10;
            min-height: 100vh;
            padding-top: 2rem;
        }

        /* 页面标题 */
        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            line-height: 1.2;
            background: linear-gradient(to right, #ffffff, #a5a6b8);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 面包屑导航 */
        .breadcrumb {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 3rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .breadcrumb a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: var(--text-primary);
        }

        .breadcrumb-separator {
            margin: 0 0.5rem;
            color: rgba(255, 255, 255, 0.3);
        }

        /* 内容卡片 */
        .content-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            animation: fadeInUp 1s ease-out 0.2s both;
        }

        .content-card:hover {
            transform: translateY(-5px);
            border-color: rgba(124, 58, 237, 0.5);
            box-shadow: 0 20px 40px rgba(124, 58, 237, 0.2);
        }

        /* 标题样式 */
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            position: relative;
            padding-left: 1rem;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        /* 表格样式 */
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            background: rgba(26, 27, 46, 0.6);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .config-table th {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .config-table td {
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            vertical-align: top;
        }

        .config-table tr:last-child td {
            border-bottom: none;
        }

        .config-table tr:hover {
            background: rgba(124, 58, 237, 0.1);
        }

        /* 链接样式 */
        .content-link {
            color: #60a5fa;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .content-link:hover {
            color: #93c5fd;
            text-decoration: underline;
        }

        /* 步骤列表 */
        .steps-list {
            list-style: none;
            counter-reset: step-counter;
        }

        .steps-list li {
            counter-increment: step-counter;
            margin-bottom: 1.5rem;
            padding-left: 3rem;
            position: relative;
        }

        .steps-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 2rem;
            height: 2rem;
            background: var(--primary-gradient);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        /* 警告框 */
        .warning-box {
            background: rgba(251, 191, 36, 0.1);
            border: 1px solid rgba(251, 191, 36, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            position: relative;
        }

        .warning-box::before {
            content: '⚠️';
            font-size: 1.25rem;
            margin-right: 0.5rem;
        }

        /* 信息框 */
        .info-box {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            position: relative;
        }

        .info-box::before {
            content: 'ℹ️';
            font-size: 1.25rem;
            margin-right: 0.5rem;
        }


        /* 响应式设计 */
        @media (max-width: 768px) {
            .content-card {
                padding: 2rem 1.5rem;
            }
            
            .config-table {
                font-size: 0.875rem;
            }
            
            .config-table th,
            .config-table td {
                padding: 0.75rem 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景 -->
    <div class="animated-bg">
        <div class="gradient-orb orb-1"></div>
        <div class="gradient-orb orb-2"></div>
        <div class="gradient-orb orb-3"></div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar py-4 relative z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-rocket text-2xl" style="color: var(--accent-purple);"></i>
                    <span class="text-xl font-bold">网红营销智能体</span>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- 语言切换 -->
                    <div class="relative">
                        <button id="languageToggle" class="flex items-center space-x-2 text-gray-300 hover:text-white transition">
                            <i class="fas fa-globe"></i>
                            <span id="currentLang">中文</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div id="languageDropdown" class="absolute top-full right-0 mt-2 bg-gray-800 border border-gray-600 rounded-lg shadow-lg hidden min-w-[140px] z-50">
                            <button class="w-full px-4 py-2 text-left hover:bg-gray-700 transition language-option flex items-center" data-lang="zh">
                                <i class="fas fa-flag mr-2 text-red-400"></i>
                                <span>中文</span>
                            </button>
                            <button class="w-full px-4 py-2 text-left hover:bg-gray-700 transition language-option flex items-center" data-lang="en">
                                <i class="fas fa-flag mr-2 text-blue-400"></i>
                                <span>English</span>
                            </button>
                        </div>
                    </div>
                    
                    <button class="btn-primary" onclick="handleButtonClick()">
                        <i class="fas fa-magic"></i>
                        申请免费试用
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container mx-auto px-6 py-8">

            <!-- 页面标题 -->
            <h1 class="page-title">企业邮箱配置指南</h1>

            <div class="max-w-4xl mx-auto">
                <!-- 主要内容区域 -->
                    <!-- 概述 -->
                    <div class="content-card">
                        <div class="mb-6">
                            <p class="text-lg text-gray-300 mb-4">
                                想通过企业邮箱来邀约网红，可以授权给第三方客户端（例如网红营销智能体平台）登录企业邮箱来发送邮件。
                            </p>
                            <p class="text-gray-300 mb-4">
                                授权只用于调用企业邮箱的发送邮件功能，本质上群发的邮件还是通过用户的企业邮箱发出，因此会受限于企业邮箱的发件规则。
                            </p>
                            <div class="warning-box">
                                <strong>注意：</strong>目前网红营销智能体支持绑定大多数企业邮箱，但Outlook邮箱由于自身服务在支持SMTP时的稳定性问题，可能存在绑定失败的情况，因此我们建议优先选用其他企业邮件服务商。
                            </div>
                        </div>
                    </div>

                    <!-- 授权方法 -->
                    <div class="content-card">
                        <h2 class="section-title">一、授权方法</h2>
                        
                        <ol class="steps-list">
                            <li>
                                <strong>进入发件配置</strong><br>
                                在邮件编辑页面点击选择发件人前面的"+"号，进入【发件配置】
                            </li>
                            <li>
                                <strong>选择企业邮箱并填写信息</strong><br>
                                选择【企业邮箱】后输入以下信息：
                                <ul class="mt-3 ml-6 space-y-2">
                                    <li><strong>邮箱地址：</strong>填写所在企业的企业邮箱地址</li>
                                    <li><strong>密码：</strong>填写企业邮箱登录密码或客户端授权码</li>
                                </ul>
                            </li>
                            <li>
                                <strong>配置高级选项</strong><br>
                                高级选项中的配置会自动关联，但部分企业邮箱需要手动修改配置：
                                <ul class="mt-3 ml-6 space-y-2">
                                    <li><strong>SMTP服务器：</strong>邮件发送服务器，输入【邮箱地址】后自动生成，但部分企业邮箱需要手动修改配置</li>
                                    <li><strong>端口：</strong>开启SSL对应端口一般是465（极少数是587，比如outlook），更改端口可能会导致发送失败</li>
                                </ul>
                            </li>
                        </ol>

                        <div class="info-box">
                            <strong>备注：</strong>目前使用网易企业邮箱、QQ邮箱、飞书企业邮箱、腾讯企业邮箱时需要【授权码】登录，授权码是企业邮箱为登录第三方客户端而引入的专用密码。有授权码时需要在【密码】处填入授权码，而非企业邮箱的登录密码。
                        </div>
                    </div>

                    <!-- SMTP配置帮助文档 -->
                    <div class="content-card">
                        <h2 class="section-title">二、SMTP配置帮助文档</h2>
                        
                        <p class="text-gray-300 mb-6">
                            不同的企业邮箱如何开启、配置SMTP服务器以及获取授权码，方法可参考用户所使用的邮件服务商的帮助文档。以下整理了部分常用的企业邮箱的帮助文档，在高级选项默认配置下无法授权成功时，请按相关文档检查配置并验证。
                        </p>

                        <table class="config-table">
                            <thead>
                                <tr>
                                    <th style="width: 22%;">邮箱服务商</th>
                                    <th style="width: 20%;">SMTP服务器</th>
                                    <th style="width: 15%;">需要授权码</th>
                                    <th style="width: 43%;">配置文档</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>腾讯企业邮箱</strong></td>
                                    <td>smtp.exmail.qq.com</td>
                                    <td><span style="color: #fbbf24;">是</span></td>
                                    <td>
                                        <a href="https://open.work.weixin.qq.com/help2/pc/19886?person_id=1&subtype=1&id=20095&no=1001570" class="content-link" target="_blank">开启POP/SMTP/IMAP功能</a><br>
                                        <a href="https://open.work.weixin.qq.com/help2/pc/20038" class="content-link" target="_blank">收发信返回错误码</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>QQ邮箱</strong></td>
                                    <td>smtp.qq.com</td>
                                    <td><span style="color: #fbbf24;">是</span></td>
                                    <td>
                                        <a href="https://service.mail.qq.com/detail/0/75" class="content-link" target="_blank">QQ邮箱授权码配置</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>网易邮箱</strong><br><small>(企业邮箱/126/163邮箱等)</small></td>
                                    <td>
                                        <a href="https://qiye.163.com/help/client-profile.html" class="content-link" target="_blank">查询SMTP服务器</a>
                                    </td>
                                    <td><span style="color: #fbbf24;">是</span></td>
                                    <td>
                                        <a href="https://qiye.163.com/help/af988e.html" class="content-link" target="_blank">网易企业邮箱授权码</a><br>
                                        <a href="https://qiye.163.com/help/l-9.html" class="content-link" target="_blank">退信常见原因</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>飞书企业邮箱</strong></td>
                                    <td>smtp.feishu.cn</td>
                                    <td><span style="color: #fbbf24;">是</span></td>
                                    <td>
                                        <a href="https://www.feishu.cn/hc/zh-CN/articles/360049068017" class="content-link" target="_blank">启用第三方客户端登录及生成专用密码</a><br>
                                        <a href="https://www.feishu.cn/hc/zh-CN/articles/654962037455" class="content-link" target="_blank">邮箱常见退信报错和解决方法</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Lark企业邮箱</strong></td>
                                    <td>smtp.larksuite.com</td>
                                    <td><span style="color: #fbbf24;">是</span></td>
                                    <td>
                                        <a href="https://www.larksuite.com/hc/en-US/articles/360048488295" class="content-link" target="_blank">Use Lark Mail on third-party email clients</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>阿里企业邮箱</strong></td>
                                    <td>smtp.mxhichina.com</td>
                                    <td><span style="color: #4ade80;">否</span></td>
                                    <td>
                                        <a href="https://help.aliyun.com/document_detail/606337.html" class="content-link" target="_blank">开启SMTP服务</a><br>
                                        <a href="https://help.aliyun.com/document_detail/36576.html" class="content-link" target="_blank">配置到第三方客户端</a><br>
                                        <a href="https://help.aliyun.com/document_detail/36612.html" class="content-link" target="_blank">常见退信报错和解决方法</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Gmail邮箱</strong></td>
                                    <td>smtp.gmail.com</td>
                                    <td><span style="color: #fbbf24;">是</span></td>
                                    <td>
                                        <a href="#" class="content-link">Gmail邮箱配置指南</a><br>
                                        <a href="https://support.google.com/a/answer/3726730" class="content-link" target="_blank">Gmail错误码</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Outlook企业邮箱</strong><br><small style="color: #fbbf24;">(不稳定)</small></td>
                                    <td>smtp-mail.outlook.com</td>
                                    <td><span style="color: #4ade80;">否</span></td>
                                    <td>
                                        <a href="https://support.microsoft.com/en-us/office/pop-imap-and-smtp-settings-for-outlook-com-d088b986-291d-42b8-9564-9c414e2aa040" class="content-link" target="_blank">Outlook SMTP配置文档</a><br>
                                        <a href="https://learn.microsoft.com/zh-cn/exchange/troubleshoot/email-delivery/ndr/non-delivery-reports-in-exchange-online" class="content-link" target="_blank">Outlook错误码</a><br>
                                        <strong><a href="#" class="content-link">Outlook邮箱配置指南</a></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>新浪企业邮箱</strong></td>
                                    <td>smtp.sina.net</td>
                                    <td><span style="color: #4ade80;">否</span></td>
                                    <td>
                                        <a href="https://mail.sina.net/enthelp.php?act=client" class="content-link" target="_blank">客户端使用方法</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>搜狐企业邮箱</strong></td>
                                    <td>smtp.sohu.com</td>
                                    <td><span style="color: #4ade80;">否</span></td>
                                    <td>
                                        <a href="https://mail.sohu.net/medias/help/help45.html" class="content-link" target="_blank">开启SMTP服务</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>ZOHO企业邮箱</strong></td>
                                    <td>smtp.zoho.com</td>
                                    <td><span style="color: #4ade80;">否</span></td>
                                    <td>
                                        <a href="https://www.zoho.com.cn/mail/help/zoho-smtp.html" class="content-link" target="_blank">配置SMTP服务器</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>亚马逊企业邮箱</strong></td>
                                    <td>smtp.mail.us-west-2.awsapps.com<br>smtp.mail.eu-west-1.awsapps.com</td>
                                    <td><span style="color: #4ade80;">否</span></td>
                                    <td>
                                        <a href="https://docs.aws.amazon.com/zh_cn/workmail/latest/userguide/using_IMAP.html" class="content-link" target="_blank">为亚马逊WorkMail设置IMAP</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>完美邮箱</strong></td>
                                    <td>smtp.88.com</td>
                                    <td><span style="color: #fbbf24;">是</span></td>
                                    <td>
                                        <a href="https://www.88.com/help/10019000079.html" class="content-link" target="_blank">配置方法</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>钉钉邮箱</strong></td>
                                    <td>smtp.em.dingtalk.com</td>
                                    <td><span style="color: #4ade80;">否</span></td>
                                    <td>
                                        <a href="https://alidocs.dingtalk.com/i/p/Y7kmbokZp3pgGLq2/docs/Gl6Pm2Db8D3mzPQGsdMwy034JxLq0Ee4" class="content-link" target="_blank">SMTP地址和端口信息</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <!-- 故障排除 -->
                    <div class="content-card">
                        <h2 class="section-title">三、邮件验证失败的常见原因及解决方法</h2>
                        
                        <h3 class="text-xl font-semibold text-white mb-3 mt-6">1. 邮箱密码填写错误</h3>
                        <p class="text-gray-300 mb-4">
                            请检查邮箱账号和密码是否正确。部分企业邮箱需要【授权码】登录，授权码是企业邮箱为登录第三方客户端而引入的专用密码，不是所有企业邮箱都需要授权码登录，但有授权码时需要在【密码】处填入授权码，而非企业邮箱的登录密码，授权码获取方法详见上方【<strong>SMTP配置帮助文档</strong>】。
                        </p>

                        <h3 class="text-xl font-semibold text-white mb-3 mt-6">2. SMTP服务器配置错误</h3>
                        <p class="text-gray-300 mb-4">
                            与企业邮箱管理员确认使用的邮件服务商品牌，按该品牌的邮件服务帮助文档来配置，比如某个企业邮箱域名，但使用的其实是阿里企业邮箱服务，需要配置阿里企业邮箱的SMTP服务器。
                        </p>

                        <h3 class="text-xl font-semibold text-white mb-3 mt-6">3. 企业的SMTP服务未开启</h3>
                        <p class="text-gray-300 mb-4">
                            邮箱地址、密码、SMTP服务器均检查配置无误，依然无法绑定成功时，有可能是企业邮箱开启了禁止使用三方客户端登录，此时需要与邮箱管理员沟通关闭禁止功能，关闭后再次尝试绑定，开启方法详见上方【<strong>SMTP配置帮助文档</strong>】。
                        </p>
                        <p class="text-gray-300 mb-4">
                            在公司未禁止使用三方客户端登录的情况下绑定失败，可检查个人邮箱账户是否开启了三方客户端安全密码/授权码登录，如果开启了需要使用安全密码登录/授权码登录或者关闭三方客户端安全密码/授权码。
                        </p>

                        <div class="info-box">
                            <strong>提示：</strong>为了验证是用户配置问题还是平台问题，可以尝试使用其他三方客户端用相同的配置信息绑定，比如使用Outlook客户端（Outlook客户端会自动给填写的邮箱解析出正确的SMTP配置信息）。
                        </div>
                    </div>

                    <!-- 常见问题 -->
                    <div class="content-card">
                        <h2 class="section-title">四、常见问题</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-2">
                                    <span class="text-purple-400">问：</span>能否使用企业邮箱以外的个人邮箱发送？
                                </h3>
                                <p class="text-gray-300 pl-8">
                                    <span class="text-green-400">答：</span>如果不想使用企业邮箱发送，建议使用Gmail邮箱授权发送，使用其他个人邮箱（如qq、163等）请参考对应邮箱配置。
                                </p>
                            </div>

                            <div>
                                <h3 class="text-lg font-semibold text-white mb-2">
                                    <span class="text-purple-400">问：</span>使用Gmail邮箱，是否也可以按照企业邮箱授权的方式来发送邮件？
                                </h3>
                                <p class="text-gray-300 pl-8">
                                    <span class="text-green-400">答：</span>Gmail邮箱推荐使用Gmail邮箱授权的方式来发送。
                                </p>
                            </div>

                            <div>
                                <h3 class="text-lg font-semibold text-white mb-2">
                                    <span class="text-purple-400">问：</span>使用Outlook企业邮箱，为什么点击【提交】后没有响应？
                                </h3>
                                <div class="text-gray-300 pl-8">
                                    <p class="mb-3">
                                        <span class="text-green-400">答：</span>Outlook官方会对部分账号增加一道验证步骤，需要用户完成验证后重新提交。验证步骤如下：
                                    </p>
                                    <ol class="list-decimal list-inside space-y-2 ml-4">
                                        <li>点击【提交】后无响应时，登录Outlook官方邮箱</li>
                                        <li>查看是否有新的安全验证邮件</li>
                                        <li>点击【This was me】验证是本人操作</li>
                                        <li>返回平台重新尝试绑定</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>

    <script>
        // 滚动时导航栏效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(15, 15, 30, 0.98)';
                navbar.classList.add('scrolled');
            } else {
                navbar.style.background = 'rgba(15, 15, 30, 0.95)';
                navbar.classList.remove('scrolled');
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 语言切换功能
        const languageToggle = document.getElementById('languageToggle');
        const languageDropdown = document.getElementById('languageDropdown');
        const currentLang = document.getElementById('currentLang');

        if (languageToggle && languageDropdown) {
            languageToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                languageDropdown.classList.toggle('hidden');
            });

            // 点击外部关闭下拉菜单
            document.addEventListener('click', function() {
                languageDropdown.classList.add('hidden');
            });

            // 语言选择
            document.querySelectorAll('.language-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const lang = this.getAttribute('data-lang');
                    currentLang.textContent = lang === 'zh' ? '中文' : 'English';
                    languageDropdown.classList.add('hidden');
                    
                    // 这里可以添加实际的语言切换逻辑
                    console.log('Language changed to:', lang);
                });
            });
        }

        // 申请免费试用按钮点击事件
        function handleButtonClick() {
            // 这里可以添加实际的跳转逻辑
            alert('申请免费试用功能暂未开放，敬请期待！');
        }
    </script>
</body>
</html>