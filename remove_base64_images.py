#!/usr/bin/env python3
"""
通用脚本：移除HTML文件中的base64图片数据
支持多种格式的base64图片数据清理
"""

import re
import sys
import os
from pathlib import Path

def remove_base64_images(html_content):
    """
    移除HTML内容中的base64图片数据
    
    Args:
        html_content (str): HTML内容
        
    Returns:
        str: 清理后的HTML内容
    """
    
    # 1. 移除data:image/格式的base64图片
    # 匹配 data:image/[格式];base64,[base64数据]
    pattern1 = r'data:image/[^;]+;base64,[A-Za-z0-9+/=]+'
    html_content = re.sub(pattern1, '', html_content)
    
    # 2. 移除CSS中的background-image base64
    # 匹配 url("data:image/...") 或 url('data:image/...')
    pattern2 = r'url\(["\']?data:image/[^;]+;base64,[A-Za-z0-9+/=]+["\']?\)'
    html_content = re.sub(pattern2, '', html_content)
    
    # 3. 移除font-face中的base64字体数据
    # 匹配 url(data:application/font-woff;base64,...)
    pattern3 = r'url\(data:application/[^;]+;base64,[A-Za-z0-9+/=]+\)'
    html_content = re.sub(pattern3, '', html_content)
    
    # 4. 移除其他类型的base64数据（如SVG等）
    # 匹配 data:[mime-type];base64,[data]
    pattern4 = r'data:[^;]+;base64,[A-Za-z0-9+/=]+'
    html_content = re.sub(pattern4, '', html_content)
    
    # 5. 清理空的src和href属性
    html_content = re.sub(r'\s+src=""', '', html_content)
    html_content = re.sub(r'\s+href=""', '', html_content)
    
    # 6. 清理多余的空格和换行
    html_content = re.sub(r'\n\s*\n', '\n', html_content)
    html_content = re.sub(r'  +', ' ', html_content)
    
    return html_content

def process_file(input_file, output_file=None):
    """
    处理HTML文件，移除base64图片
    
    Args:
        input_file (str): 输入文件路径
        output_file (str, optional): 输出文件路径，默认为原文件名_cleaned
    """
    
    try:
        # 读取原文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"原文件大小: {len(content)} 字符")
        
        # 移除base64图片
        cleaned_content = remove_base64_images(content)
        
        print(f"清理后大小: {len(cleaned_content)} 字符")
        print(f"减少了: {len(content) - len(cleaned_content)} 字符 ({((len(content) - len(cleaned_content)) / len(content) * 100):.1f}%)")
        
        # 确定输出文件名
        if output_file is None:
            input_path = Path(input_file)
            output_file = input_path.parent / f"{input_path.stem}_cleaned{input_path.suffix}"
        
        # 写入清理后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print(f"已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False
    
    return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python remove_base64_images.py <input_file> [output_file]")
        print("示例: python remove_base64_images.py input.html output.html")
        print("如果不指定输出文件，将自动生成 [原文件名]_cleaned.html")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(input_file):
        print(f"错误: 文件 '{input_file}' 不存在")
        sys.exit(1)
    
    success = process_file(input_file, output_file)
    
    if success:
        print("处理完成！")
    else:
        print("处理失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()